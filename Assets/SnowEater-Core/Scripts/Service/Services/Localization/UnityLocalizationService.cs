// Copyright Isto Inc.

using SnowEater.Core.Localization;
using SnowEater.Core.Systems;
using System;
using UnityEngine;
using UnityEngine.Localization.Settings;
using System.Linq;

namespace SnowEater.Core.Services
{
    [CreateAssetMenu(
        fileName = nameof(UnityLocalizationService),
        menuName = "Snow Eater/Services/" + nameof(UnityLocalizationService))]
    public class UnityLocalizationService : Service, ILocalization
    {
        public override Type GetServiceInterface() => typeof(ILocalization);

        public LocalizationLanguageEnum CurrentLanguage
        {
            get
            {
                var locale = LocalizationSettings.SelectedLocale;
                return LocalizationLanguageEnum.GetByValue(locale.Identifier.Code);
            }
        }

        public async void SetLanguage(LocalizationLanguageEnum language)
        {
            // Ensure the localization system is initialized
            await LocalizationSettings.InitializationOperation.Task;

            // Find the locale matching the enum's locale code
            var locale = LocalizationSettings.AvailableLocales.Locales
                .FirstOrDefault(l => l.Identifier.Code == language.Value);

            if (locale != null)
            {
                LocalizationSettings.SelectedLocale = locale;
            }
            else
            {
                Debug.LogWarning($"Locale '{language.Value}' not found.");
            }
        }

        public string GetLocalizedText(string key)
        {
            var tables = LocalizationSettings.StringDatabase.GetAllTables();
            tables.WaitForCompletion();

            string localizedValue = "LOCALIZATION_NOT_FOUND";
            foreach (var table in tables.Result)
            {
                Debug.Log("Loaded " + table.TableCollectionName);
                var op = LocalizationSettings.StringDatabase.GetLocalizedString(table.name,key);
                if (string.IsNullOrEmpty(op))
                {
                    continue;
                }
                else
                {
                    localizedValue = op;
                    break;
                }
            }

            return localizedValue;
        }
    }
}