// Copyright Isto Inc.

using UnityEngine;

namespace SnowEater.Core.Localization
{
    public static class LocTermFactory
    {
        /// <summary>
        /// Creates a single LocTerm given its key and default/text value.
        /// </summary>
        /// <param name="key">The unique identifier of the term.</param>
        /// <param name="defaultValue">The fallback or default text.</param>
        public static LocTerm Create(LocalizationType localizationType, string key)
        {
            LocTerm locTerm = null;

            if(localizationType == LocalizationType.Localized)
            {
                locTerm = new SnowEaterLocalizedString(key);
            }
            else if (localizationType == LocalizationType.NonLocalized)
            {
                locTerm = new SnowEaterNonLocalizedString(key);
            }
            else
            {
                Debug.LogError($"Localization type {localizationType} not supported.");
            }

            return locTerm;
        }
    }
}