// Copyright Isto Inc.

using SnowEater.Core.Services;
using UnityEngine.UIElements;

namespace SnowEater.Core.Localization
{
    public abstract class LocTerm
    {
        protected readonly string _key;

        private ILocalization _localizationService;

        protected LocTerm(string key)
        {
            _key = key;
            _localizationService = Systems.Services.Get<ILocalization>();
        }

        public string GetKey()
        {
            return _key;
        }

        public virtual string Localize()
        {
            return _localizationService.GetLocalizedText(_key);
        }

        public void LocalizeInto(TextElement textElement)
        {
            if(textElement == null) return;
             textElement.text = Localize();
        }

        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }

            LocTerm other = (LocTerm)otherObject;
            return _key.Equals(other._key);
        }

        public override int GetHashCode()
        {
            return _key != null ? _key.GetHashCode() : 0;
        }
    }
}